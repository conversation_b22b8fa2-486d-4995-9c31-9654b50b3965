import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Globe, MapPin, Users, ArrowRight } from 'lucide-react';
import Card from '../UI/Card';

const RegionalSection: React.FC = () => {
  const [activeRegion, setActiveRegion] = useState(0);

  const regions = [
    {
      name: 'Europe & Latin America',
      lead: '<PERSON>',
      email: '<EMAIL>',
      color: 'from-blue-500 to-blue-600',
      countries: [
        'Spain', 'Portugal', 'Italy', 'France', 'Germany', 'UK',
        'Brazil', 'Argentina', 'Chile', 'Colombia', 'Mexico', 'Peru'
      ],
      stats: {
        countries: 12,
        projects: '150+',
        volume: '$2.5B'
      },
      description: 'Managing diverse markets across European and Latin American regions with specialized expertise in both developed and emerging economies.'
    },
    {
      name: 'Africa',
      lead: '<PERSON>',
      email: '<EMAIL>',
      color: 'from-green-500 to-green-600',
      countries: [
        'Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan',
        'Chad', 'CAR', 'DRC', 'Cameroon', 'Nigeria', 'Kenya', 'Ethiopia', 'Ghana', 'Senegal'
      ],
      stats: {
        countries: 15,
        projects: '200+',
        volume: '$3.2B'
      },
      description: 'Comprehensive coverage across North, Central, and West Africa, supporting sustainable development and economic growth initiatives.'
    },
    {
      name: 'Asia',
      lead: 'Abdulla Al Mansoori',
      email: '<EMAIL>',
      color: 'from-purple-500 to-purple-600',
      countries: [
        'Malaysia', 'Indonesia', 'Thailand', 'Vietnam', 'Philippines', 'Singapore',
        'Kazakhstan', 'Uzbekistan', 'Kyrgyzstan', 'Tajikistan', 'Afghanistan', 'Pakistan', 'India', 'Bangladesh'
      ],
      stats: {
        countries: 14,
        projects: '180+',
        volume: '$2.8B'
      },
      description: 'Strategic operations across Southeast Asia and Central Asia, fostering regional cooperation and sustainable development.'
    }
  ];

  return (
    <section id="regions" className="section-padding bg-gradient-to-br from-navy-50 to-primary-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-navy-900 mb-6">
            Global Regional
            <span className="gradient-text block">Operations</span>
          </h2>
          <p className="text-xl text-navy-600 max-w-3xl mx-auto leading-relaxed">
            Three specialized regional teams managing withdrawal requests across 41 countries 
            with dedicated leadership and local expertise.
          </p>
        </motion.div>

        {/* Region Selector */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {regions.map((region, index) => (
            <motion.button
              key={region.name}
              onClick={() => setActiveRegion(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                activeRegion === index
                  ? `bg-gradient-to-r ${region.color} text-white shadow-lg`
                  : 'bg-white text-navy-700 hover:bg-navy-50 border border-navy-200'
              }`}
            >
              {region.name}
            </motion.button>
          ))}
        </motion.div>

        {/* Active Region Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeRegion}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ duration: 0.5 }}
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Region Info */}
              <div>
                <Card className="p-8">
                  <div className="flex items-center mb-6">
                    <div className={`w-12 h-12 bg-gradient-to-r ${regions[activeRegion].color} rounded-lg flex items-center justify-center mr-4`}>
                      <Globe className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-navy-900">
                        {regions[activeRegion].name}
                      </h3>
                      <p className="text-navy-600">Regional Operations</p>
                    </div>
                  </div>

                  <p className="text-navy-600 mb-6 leading-relaxed">
                    {regions[activeRegion].description}
                  </p>

                  {/* Regional Lead */}
                  <div className="bg-navy-50 rounded-lg p-4 mb-6">
                    <div className="flex items-center mb-2">
                      <Users className="w-5 h-5 text-navy-600 mr-2" />
                      <span className="font-semibold text-navy-900">Regional Lead</span>
                    </div>
                    <p className="text-navy-800 font-medium">{regions[activeRegion].lead}</p>
                    <p className="text-navy-600 text-sm">{regions[activeRegion].email}</p>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-navy-900">
                        {regions[activeRegion].stats.countries}
                      </div>
                      <div className="text-sm text-navy-600">Countries</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-navy-900">
                        {regions[activeRegion].stats.projects}
                      </div>
                      <div className="text-sm text-navy-600">Projects</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-navy-900">
                        {regions[activeRegion].stats.volume}
                      </div>
                      <div className="text-sm text-navy-600">Volume</div>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Countries Grid */}
              <div>
                <Card className="p-8">
                  <div className="flex items-center mb-6">
                    <MapPin className="w-6 h-6 text-navy-600 mr-2" />
                    <h4 className="text-xl font-bold text-navy-900">
                      Covered Countries
                    </h4>
                  </div>
                  
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                    {regions[activeRegion].countries.map((country, index) => (
                      <motion.div
                        key={country}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.05 }}
                        className="bg-white border border-navy-200 rounded-lg p-3 text-center hover:shadow-md transition-shadow duration-300"
                      >
                        <span className="text-navy-700 font-medium text-sm">
                          {country}
                        </span>
                      </motion.div>
                    ))}
                  </div>

                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    className="mt-6 pt-6 border-t border-navy-200"
                  >
                    <button className="flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors duration-300">
                      <span>View Regional Dashboard</span>
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </motion.div>
                </Card>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Global Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="mt-16"
        >
          <Card className="p-8 bg-gradient-to-r from-navy-900 to-navy-800 text-white">
            <div className="text-center">
              <h3 className="text-3xl font-bold mb-4">
                Global Impact & Reach
              </h3>
              <p className="text-navy-200 mb-8 max-w-3xl mx-auto">
                Coordinated operations across three major regions, ensuring efficient 
                fund disbursement and sustainable development impact worldwide.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                  <div className="text-4xl font-bold text-gold-400 mb-2">41</div>
                  <div className="text-navy-300">Total Countries</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-gold-400 mb-2">530+</div>
                  <div className="text-navy-300">Active Projects</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-gold-400 mb-2">$8.5B</div>
                  <div className="text-navy-300">Total Volume</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-gold-400 mb-2">24/7</div>
                  <div className="text-navy-300">Operations</div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default RegionalSection;
