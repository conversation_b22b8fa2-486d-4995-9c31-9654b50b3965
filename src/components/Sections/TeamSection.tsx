import React from 'react';
import { motion } from 'framer-motion';
import { Users, Eye, Building, CreditCard, ArrowRight, Mail } from 'lucide-react';
import Card from '../UI/Card';
import Button from '../UI/Button';

const TeamSection: React.FC = () => {
  const teamRoles = [
    {
      role: 'Archive Team',
      icon: Building,
      color: 'from-blue-500 to-blue-600',
      description: 'Create and submit withdrawal requests with AI-powered OCR assistance',
      permissions: ['Create Requests', 'Upload Documents', 'OCR Processing', 'View Own Requests'],
      members: [
        { name: '<PERSON>', email: '<EMAIL>', region: undefined, title: undefined },
        { name: '<PERSON><PERSON>', email: 'mkalm<PERSON><PERSON>@adfd.ae', region: undefined, title: undefined }
      ],
      ctaText: 'Access Archive Portal',
      ctaAction: 'archive-login'
    },
    {
      role: 'Operations Teams',
      icon: Users,
      color: 'from-green-500 to-green-600',
      description: 'Review and approve/reject requests within assigned regional scope',
      permissions: ['Regional Access', 'Approve/Reject', 'Technical Review', 'Comment & Track'],
      members: [
        { name: '<PERSON>', email: '<EMAIL>', region: 'Europe & Latin America', title: undefined },
        { name: 'Ahmed Al Kalbani', email: '<EMAIL>', region: 'Africa', title: undefined },
        { name: 'Abdulla Al Mansoori', email: '<EMAIL>', region: 'Asia', title: undefined }
      ],
      ctaText: 'Operations Dashboard',
      ctaAction: 'operations-login'
    },
    {
      role: 'Core Banking Team',
      icon: CreditCard,
      color: 'from-purple-500 to-purple-600',
      description: 'Process final disbursements and complete withdrawal requests',
      permissions: ['Disburse Funds', 'Final Approval', 'Banking Integration', 'Completion Tracking'],
      members: [
        { name: 'Ahmed Siddique', email: '<EMAIL>', region: undefined, title: undefined },
        { name: 'Yazan Jamous', email: '<EMAIL>', region: undefined, title: undefined },
        { name: 'Ameer Hamza', email: '<EMAIL>', region: undefined, title: undefined }
      ],
      ctaText: 'Banking Portal',
      ctaAction: 'banking-login'
    },
    {
      role: 'Observers',
      icon: Eye,
      color: 'from-orange-500 to-orange-600',
      description: 'Monitor system-wide operations with comprehensive oversight access',
      permissions: ['View All Requests', 'Global Analytics', 'Audit Reports', 'Comment & Coordinate'],
      members: [
        { name: 'Fatima Al Hammadi', email: '<EMAIL>', region: undefined, title: 'Loan Administrator' },
        { name: 'Adel Al Hosani', email: '<EMAIL>', region: undefined, title: 'Head of Operations' }
      ],
      ctaText: 'Observer Dashboard',
      ctaAction: 'observer-login'
    }
  ];

  return (
    <section id="team" className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-navy-900 mb-6">
            ADFD Team
            <span className="gradient-text block">Access Portals</span>
          </h2>
          <p className="text-xl text-navy-600 max-w-3xl mx-auto leading-relaxed">
            Role-based access portals designed for each team member's specific responsibilities 
            and regional assignments within the Abu Dhabi Fund for Development.
          </p>
        </motion.div>

        {/* Team Roles Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {teamRoles.map((team, index) => (
            <motion.div
              key={team.role}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="p-8 h-full group hover:shadow-2xl transition-all duration-300">
                <div className="flex items-start space-x-4 mb-6">
                  <div className={`w-14 h-14 bg-gradient-to-r ${team.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <team.icon className="w-7 h-7 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-navy-900 mb-2">
                      {team.role}
                    </h3>
                    <p className="text-navy-600 leading-relaxed">
                      {team.description}
                    </p>
                  </div>
                </div>

                {/* Permissions */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-navy-700 mb-3 uppercase tracking-wide">
                    Key Permissions
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {team.permissions.map((permission) => (
                      <div key={permission} className="flex items-center text-sm text-navy-600">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-2"></div>
                        {permission}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Team Members */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-navy-700 mb-3 uppercase tracking-wide">
                    Team Members
                  </h4>
                  <div className="space-y-2">
                    {team.members.map((member) => (
                      <div key={member.email} className="flex items-center justify-between bg-navy-50 rounded-lg p-3">
                        <div>
                          <div className="font-medium text-navy-900">{member.name}</div>
                          <div className="text-sm text-navy-600">{member.email}</div>
                          {member.region && (
                            <div className="text-xs text-primary-600 font-medium">{member.region}</div>
                          )}
                          {member.title && (
                            <div className="text-xs text-gold-600 font-medium">{member.title}</div>
                          )}
                        </div>
                        <Mail className="w-4 h-4 text-navy-400" />
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA Button */}
                <Button
                  variant="primary"
                  className={`w-full bg-gradient-to-r ${team.color} hover:opacity-90`}
                  icon={ArrowRight}
                  iconPosition="right"
                >
                  {team.ctaText}
                </Button>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* System Access Information */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card className="p-8 bg-gradient-to-r from-navy-50 to-primary-50 border-navy-200">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-navy-900 mb-4">
                Secure System Access
              </h3>
              <p className="text-navy-600 mb-6 max-w-3xl mx-auto">
                All team members access the system through secure, role-based portals with 
                multi-factor authentication and regional access controls. Contact your system 
                administrator for account setup and access credentials.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-navy-900 mb-1">10 Team Members</h4>
                  <p className="text-sm text-navy-600">Across 4 specialized roles</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Eye className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-navy-900 mb-1">Role-Based Access</h4>
                  <p className="text-sm text-navy-600">Strict permission controls</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Building className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-navy-900 mb-1">3 Global Regions</h4>
                  <p className="text-sm text-navy-600">Regional team assignments</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default TeamSection;
