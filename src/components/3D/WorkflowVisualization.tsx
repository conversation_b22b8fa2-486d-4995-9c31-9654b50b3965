import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text, Sphere, Line, OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';

// Workflow Stage Component
const WorkflowStage: React.FC<{
  position: [number, number, number];
  color: string;
  label: string;
  active: boolean;
  index: number;
}> = ({ position, color, label, active, index }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const textRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.1;
    }
  });

  return (
    <group position={position}>
      {/* Stage Sphere */}
      <Sphere ref={meshRef} args={[0.8, 32, 32]}>
        <meshStandardMaterial
          color={color}
          emissive={active ? color : '#000000'}
          emissiveIntensity={active ? 0.3 : 0}
          metalness={0.8}
          roughness={0.2}
        />
      </Sphere>
      
      {/* Glow Effect */}
      {active && (
        <Sphere args={[1.2, 32, 32]}>
          <meshBasicMaterial
            color={color}
            transparent
            opacity={0.2}
          />
        </Sphere>
      )}
      
      {/* Stage Label */}
      <Text
        ref={textRef}
        position={[0, -1.5, 0]}
        fontSize={0.3}
        color="#1e293b"
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-bold.woff"
      >
        {label}
      </Text>
    </group>
  );
};

// Connection Line Component
const ConnectionLine: React.FC<{
  start: [number, number, number];
  end: [number, number, number];
  active: boolean;
}> = ({ start, end, active }) => {
  const points = useMemo(() => [
    new THREE.Vector3(...start),
    new THREE.Vector3(...end),
  ], [start, end]);

  return (
    <Line
      points={points}
      color={active ? "#0ea5e9" : "#94a3b8"}
      lineWidth={active ? 4 : 2}
      transparent
      opacity={active ? 0.8 : 0.4}
    />
  );
};

// Floating Particles Component
const FloatingParticles: React.FC = () => {
  const particlesRef = useRef<THREE.Points>(null);
  
  const particles = useMemo(() => {
    const positions = new Float32Array(100 * 3);
    for (let i = 0; i < 100; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
    }
    return positions;
  }, []);

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          args={[particles, 3]}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.05}
        color="#f59e0b"
        transparent
        opacity={0.6}
      />
    </points>
  );
};

// Main 3D Scene Component
const WorkflowScene: React.FC<{ activeStage: number }> = ({ activeStage }) => {
  const stages = [
    { label: "Initial\nReview", color: "#f97316", position: [-6, 0, 0] as [number, number, number] },
    { label: "Technical\nReview", color: "#eab308", position: [-2, 0, 0] as [number, number, number] },
    { label: "Core\nBanking", color: "#22c55e", position: [2, 0, 0] as [number, number, number] },
    { label: "Disbursed", color: "#06b6d4", position: [6, 0, 0] as [number, number, number] },
  ];

  const connections = [
    { start: [-6, 0, 0] as [number, number, number], end: [-2, 0, 0] as [number, number, number] },
    { start: [-2, 0, 0] as [number, number, number], end: [2, 0, 0] as [number, number, number] },
    { start: [2, 0, 0] as [number, number, number], end: [6, 0, 0] as [number, number, number] },
  ];

  return (
    <>
      <Environment preset="city" />
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} intensity={1} />
      <pointLight position={[-10, -10, -10]} intensity={0.5} />
      
      <FloatingParticles />
      
      {/* Workflow Stages */}
      {stages.map((stage, index) => (
        <WorkflowStage
          key={index}
          position={stage.position}
          color={stage.color}
          label={stage.label}
          active={index <= activeStage}
          index={index}
        />
      ))}
      
      {/* Connection Lines */}
      {connections.map((connection, index) => (
        <ConnectionLine
          key={index}
          start={connection.start}
          end={connection.end}
          active={index < activeStage}
        />
      ))}
      
      <OrbitControls
        enableZoom={false}
        enablePan={false}
        maxPolarAngle={Math.PI / 2}
        minPolarAngle={Math.PI / 2}
        autoRotate
        autoRotateSpeed={0.5}
      />
    </>
  );
};

// Main Component
const WorkflowVisualization: React.FC = () => {
  const [activeStage, setActiveStage] = React.useState(0);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setActiveStage((prev) => (prev + 1) % 4);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="w-full h-96 lg:h-[500px]">
      <Canvas
        camera={{ position: [0, 0, 12], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        <WorkflowScene activeStage={activeStage} />
      </Canvas>
    </div>
  );
};

export default WorkflowVisualization;
