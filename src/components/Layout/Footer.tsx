import React from 'react';
import { motion } from 'framer-motion';
import { Shield, MapPin, Globe } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: 'System',
      links: [
        { name: 'Features', href: '#features' },
        { name: 'Security', href: '#security' },
        { name: 'Workflow', href: '#workflow' },
        { name: 'Documentation', href: '#docs' },
      ]
    },
    {
      title: 'Regions',
      links: [
        { name: 'Europe & Latin America', href: '#europe-latam' },
        { name: 'Africa', href: '#africa' },
        { name: 'Asia', href: '#asia' },
        { name: 'Global Operations', href: '#global' },
      ]
    },
    {
      title: 'Support',
      links: [
        { name: 'Help Center', href: '#help' },
        { name: 'Training', href: '#training' },
        { name: 'Contact', href: '#contact' },
        { name: 'System Status', href: '#status' },
      ]
    }
  ];

  return (
    <footer className="bg-navy-900 text-white">
      <div className="container-custom section-padding">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:col-span-1"
          >
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center">
                <Shield className="w-7 h-7 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">ADFD</h3>
                <p className="text-navy-300">Tracking System</p>
              </div>
            </div>
            <p className="text-navy-300 mb-6 leading-relaxed">
              Abu Dhabi Fund for Development's enterprise-grade withdrawal request tracking system with banking-level security and intelligent automation.
            </p>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-navy-300">
                <MapPin className="w-4 h-4 text-gold-400" />
                <span className="text-sm">Al Buteen P.O. Box: 814, Abu Dhabi, UAE</span>
              </div>
              <div className="flex items-center space-x-3 text-navy-300">
                <Globe className="w-4 h-4 text-gold-400" />
                <span className="text-sm">adfd.ae</span>
              </div>
            </div>
          </motion.div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <h4 className="text-lg font-semibold mb-6 text-white">{section.title}</h4>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <motion.a
                      href={link.href}
                      whileHover={{ x: 5 }}
                      className="text-navy-300 hover:text-gold-400 transition-colors duration-300 text-sm"
                    >
                      {link.name}
                    </motion.a>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="border-t border-navy-700 mt-12 pt-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-navy-400 text-sm">
              © {currentYear} Abu Dhabi Fund for Development. All rights reserved.
            </div>
            <div className="flex items-center space-x-6">
              <a href="#privacy" className="text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm">
                Privacy Policy
              </a>
              <a href="#terms" className="text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm">
                Terms of Service
              </a>
              <a href="#security" className="text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm">
                Security
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
