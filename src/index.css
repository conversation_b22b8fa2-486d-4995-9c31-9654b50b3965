@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans antialiased;
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    @apply border-border;
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-gold-500 bg-clip-text text-transparent;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .section-padding {
    @apply py-20 px-4 sm:px-6 lg:px-8;
  }

  .container-custom {
    @apply max-w-7xl mx-auto;
  }
}
