[{"/Users/<USER>/ADFD_Tracking_System/src/index.tsx": "1", "/Users/<USER>/ADFD_Tracking_System/src/reportWebVitals.ts": "2", "/Users/<USER>/ADFD_Tracking_System/src/App.tsx": "3", "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/HeroSection.tsx": "4", "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/FeaturesSection.tsx": "5", "/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Header.tsx": "6", "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/RegionalSection.tsx": "7", "/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Footer.tsx": "8", "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/TeamSection.tsx": "9", "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/SecuritySection.tsx": "10", "/Users/<USER>/ADFD_Tracking_System/src/components/Effects/ScrollEffects.tsx": "11", "/Users/<USER>/ADFD_Tracking_System/src/components/UI/Button.tsx": "12", "/Users/<USER>/ADFD_Tracking_System/src/components/UI/Card.tsx": "13", "/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx": "14"}, {"size": 554, "mtime": 1753110560629, "results": "15", "hashOfConfig": "16"}, {"size": 425, "mtime": 1753110560630, "results": "17", "hashOfConfig": "16"}, {"size": 2040, "mtime": 1753111078987, "results": "18", "hashOfConfig": "16"}, {"size": 7983, "mtime": 1753110808066, "results": "19", "hashOfConfig": "16"}, {"size": 6684, "mtime": 1753110849904, "results": "20", "hashOfConfig": "16"}, {"size": 4685, "mtime": 1753110682475, "results": "21", "hashOfConfig": "16"}, {"size": 10417, "mtime": 1753111300688, "results": "22", "hashOfConfig": "16"}, {"size": 4839, "mtime": 1753111289367, "results": "23", "hashOfConfig": "16"}, {"size": 9697, "mtime": 1753111277515, "results": "24", "hashOfConfig": "16"}, {"size": 10095, "mtime": 1753110935003, "results": "25", "hashOfConfig": "16"}, {"size": 6296, "mtime": 1753111226312, "results": "26", "hashOfConfig": "16"}, {"size": 2717, "mtime": 1753110722847, "results": "27", "hashOfConfig": "16"}, {"size": 1105, "mtime": 1753110733430, "results": "28", "hashOfConfig": "16"}, {"size": 5570, "mtime": 1753111214550, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f9paq8", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/ADFD_Tracking_System/src/index.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/reportWebVitals.ts", [], [], "/Users/<USER>/ADFD_Tracking_System/src/App.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/HeroSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/FeaturesSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Header.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/RegionalSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Footer.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/TeamSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Sections/SecuritySection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Effects/ScrollEffects.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/UI/Button.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/UI/Card.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx", [], []]